#!/usr/bin/env python3
"""
Matrix AI Launcher - Starts the spectacular startup animation and then launches the main application
"""

import subprocess
import threading
import time
import os
import sys

def run_startup_animation():
    """Run the Matrix startup animation"""
    try:
        print("Starting Matrix AI Startup Animation...")
        subprocess.run([sys.executable, "matrix_startup_animation.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"Animation process ended: {e}")
    except Exception as e:
        print(f"Error running startup animation: {e}")

def run_main_application():
    """Run the main Matrix AI application"""
    try:
        # Wait a moment for the animation to start
        time.sleep(1)
        print("Starting Matrix AI Main Application...")
        subprocess.run([sys.executable, "main.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"Main application process ended: {e}")
    except Exception as e:
        print(f"Error running main application: {e}")

def main():
    """Main launcher function"""
    print("=" * 60)
    print("MATRIX AI - SPECTACULAR STARTUP SEQUENCE")
    print("=" * 60)
    
    # Create threads for both processes
    animation_thread = threading.Thread(target=run_startup_animation, daemon=True)
    app_thread = threading.Thread(target=run_main_application, daemon=True)
    
    # Start the animation first
    animation_thread.start()
    
    # Start the main application after a short delay
    time.sleep(0.5)
    app_thread.start()
    
    # Wait for both threads
    try:
        animation_thread.join()
        app_thread.join()
    except KeyboardInterrupt:
        print("\nShutting down Matrix AI...")
    
    print("Matrix AI Session Complete!")

if __name__ == "__main__":
    main()
